/**
 * Interactive Grid Pattern Component
 * Vanilla JavaScript implementation of Magic UI's InteractiveGridPattern
 * Optimized for black backgrounds with subtle white/gray tones
 */

class InteractiveGridPattern {
    constructor(container, options = {}) {
        this.container = typeof container === 'string' ? document.querySelector(container) : container;
        
        if (!this.container) {
            console.warn('InteractiveGridPattern: Container not found');
            return;
        }

        // Default options optimized for black background
        this.options = {
            cellSize: 30,
            gridColor: 'rgba(255, 255, 255, 0.03)',
            hoverColor: 'rgba(255, 255, 255, 0.06)',
            borderColor: 'rgba(255, 255, 255, 0.02)',
            transitionDuration: '200ms',
            className: 'interactive-grid-pattern',
            zIndex: -1,
            ...options
        };

        this.svg = null;
        this.squares = [];
        this.hoveredSquare = null;
        this.isInitialized = false;
        this.resizeObserver = null;

        this.init();
    }

    init() {
        if (this.isInitialized) return;
        
        this.createSVG();
        this.setupEventListeners();
        this.setupResizeObserver();
        this.render();
        
        this.isInitialized = true;
    }

    createSVG() {
        this.svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
        this.svg.setAttribute('class', this.options.className);
        this.svg.style.cssText = `
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: ${this.options.zIndex};
            border: 1px solid ${this.options.borderColor};
        `;

        this.container.style.position = this.container.style.position || 'relative';
        this.container.appendChild(this.svg);
    }

    calculateGrid() {
        const rect = this.container.getBoundingClientRect();
        const width = rect.width;
        const height = rect.height;
        
        const cols = Math.floor(width / this.options.cellSize);
        const rows = Math.floor(height / this.options.cellSize);
        
        return { width, height, cols, rows };
    }

    render() {
        if (!this.svg) return;

        const { width, height, cols, rows } = this.calculateGrid();
        
        this.svg.setAttribute('width', width);
        this.svg.setAttribute('height', height);
        this.svg.innerHTML = '';
        this.squares = [];

        // Create grid squares
        for (let i = 0; i < cols * rows; i++) {
            const col = i % cols;
            const row = Math.floor(i / cols);
            const x = col * this.options.cellSize;
            const y = row * this.options.cellSize;

            const rect = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
            rect.setAttribute('x', x);
            rect.setAttribute('y', y);
            rect.setAttribute('width', this.options.cellSize);
            rect.setAttribute('height', this.options.cellSize);
            rect.setAttribute('fill', 'transparent');
            rect.setAttribute('stroke', this.options.gridColor);
            rect.setAttribute('stroke-width', '0.5');
            rect.style.cssText = `
                transition: fill ${this.options.transitionDuration} ease-in-out;
                cursor: pointer;
                pointer-events: all;
            `;

            rect.addEventListener('mouseenter', () => this.handleSquareHover(i, rect));
            rect.addEventListener('mouseleave', () => this.handleSquareLeave(i, rect));

            this.svg.appendChild(rect);
            this.squares.push(rect);
        }
    }

    handleSquareHover(index, rect) {
        if (this.hoveredSquare === index) return;
        
        this.hoveredSquare = index;
        rect.setAttribute('fill', this.options.hoverColor);
        
        // Add subtle scale effect
        rect.style.transform = 'scale(0.95)';
        rect.style.transformOrigin = 'center';
    }

    handleSquareLeave(index, rect) {
        if (this.hoveredSquare !== index) return;
        
        this.hoveredSquare = null;
        rect.setAttribute('fill', 'transparent');
        rect.style.transform = 'scale(1)';
    }

    setupEventListeners() {
        // Handle container visibility changes
        if ('IntersectionObserver' in window) {
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        this.svg.style.display = 'block';
                    } else {
                        this.svg.style.display = 'none';
                    }
                });
            });
            observer.observe(this.container);
        }
    }

    setupResizeObserver() {
        if ('ResizeObserver' in window) {
            this.resizeObserver = new ResizeObserver(() => {
                this.render();
            });
            this.resizeObserver.observe(this.container);
        } else {
            // Fallback for browsers without ResizeObserver
            window.addEventListener('resize', () => {
                this.render();
            });
        }
    }

    updateOptions(newOptions) {
        this.options = { ...this.options, ...newOptions };
        this.render();
    }

    destroy() {
        if (this.resizeObserver) {
            this.resizeObserver.disconnect();
        }
        
        if (this.svg && this.svg.parentNode) {
            this.svg.parentNode.removeChild(this.svg);
        }
        
        this.isInitialized = false;
        this.squares = [];
        this.hoveredSquare = null;
    }
}

// Factory function for easy initialization
function createInteractiveGrid(container, options = {}) {
    return new InteractiveGridPattern(container, options);
}

// Auto-initialize grids with data attributes
document.addEventListener('DOMContentLoaded', function() {
    const gridContainers = document.querySelectorAll('[data-interactive-grid]');
    
    gridContainers.forEach(container => {
        const options = {};
        
        // Parse data attributes for options
        if (container.dataset.cellSize) {
            options.cellSize = parseInt(container.dataset.cellSize);
        }
        if (container.dataset.gridColor) {
            options.gridColor = container.dataset.gridColor;
        }
        if (container.dataset.hoverColor) {
            options.hoverColor = container.dataset.hoverColor;
        }
        
        new InteractiveGridPattern(container, options);
    });
});

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { InteractiveGridPattern, createInteractiveGrid };
}

// Global access
window.InteractiveGridPattern = InteractiveGridPattern;
window.createInteractiveGrid = createInteractiveGrid;
