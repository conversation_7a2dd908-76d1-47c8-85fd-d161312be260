/**
 * Interactive Grid Pattern Styles
 * Optimized for black backgrounds with subtle effects
 */

/* CSS Custom Properties for easy customization */
:root {
  --grid-cell-size: 30px;
  --grid-color: rgba(255, 255, 255, 0.03);
  --grid-hover-color: rgba(255, 255, 255, 0.06);
  --grid-border-color: rgba(255, 255, 255, 0.02);
  --grid-transition-duration: 200ms;
  --grid-transition-easing: ease-in-out;
}

/* Base grid pattern styles */
.interactive-grid-pattern {
  position: absolute !important;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: -1;
  opacity: 1;
  transition: opacity var(--grid-transition-duration) var(--grid-transition-easing);
}

/* Grid container positioning */
.grid-pattern-container {
  position: relative;
  overflow: hidden;
}

/* Grid squares base styles */
.interactive-grid-pattern rect {
  transition: all var(--grid-transition-duration) var(--grid-transition-easing);
  cursor: pointer;
  pointer-events: all;
}

/* Hover effects for grid squares */
.interactive-grid-pattern rect:hover {
  fill: var(--grid-hover-color) !important;
  transform: scale(0.95);
  transform-origin: center;
}

/* Section-specific adaptations */

/* About section grid - works with geometric elements */
#about .interactive-grid-pattern {
  --grid-color: rgba(255, 255, 255, 0.025);
  --grid-hover-color: rgba(59, 130, 246, 0.04); /* Subtle primary color hint */
  z-index: 0; /* Above base background, below geometric elements */
}

/* Services section grid - complements bento grid */
#services .interactive-grid-pattern {
  --grid-color: rgba(255, 255, 255, 0.03);
  --grid-hover-color: rgba(255, 255, 255, 0.06);
  z-index: -1; /* Behind service cards */
}

/* Portfolio section grid - works with gradient blur circles */
#portfolio .interactive-grid-pattern {
  --grid-color: rgba(255, 255, 255, 0.025);
  --grid-hover-color: rgba(16, 185, 129, 0.04); /* Subtle emerald hint */
  z-index: 0; /* Above blur circles, below content */
}

/* Testimonials section grid */
#testimonials .interactive-grid-pattern {
  --grid-color: rgba(255, 255, 255, 0.03);
  --grid-hover-color: rgba(255, 255, 255, 0.06);
  z-index: -1;
}

/* FAQ section grid - adapted for gradient background */
#faq .interactive-grid-pattern {
  --grid-color: rgba(255, 255, 255, 0.02);
  --grid-hover-color: rgba(59, 130, 246, 0.03);
  z-index: 0; /* Above gradient stripe, below content */
}

/* Contact section grid - works with map overlay */
#contact .interactive-grid-pattern {
  --grid-color: rgba(255, 255, 255, 0.025);
  --grid-hover-color: rgba(255, 255, 255, 0.05);
  z-index: 1; /* Above map, below overlay gradient */
}

/* Responsive adjustments */
@media (max-width: 768px) {
  :root {
    --grid-cell-size: 25px;
  }
  
  .interactive-grid-pattern {
    opacity: 0.7; /* Slightly more subtle on mobile */
  }
}

@media (max-width: 480px) {
  :root {
    --grid-cell-size: 20px;
  }
  
  .interactive-grid-pattern {
    opacity: 0.5; /* Even more subtle on small screens */
  }
}

/* High DPI display optimizations */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .interactive-grid-pattern rect {
    stroke-width: 0.25px; /* Thinner lines on high DPI */
  }
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  .interactive-grid-pattern,
  .interactive-grid-pattern rect {
    transition: none !important;
  }
  
  .interactive-grid-pattern rect:hover {
    transform: none !important;
  }
}

/* Performance optimizations */
.interactive-grid-pattern {
  will-change: opacity;
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
}

.interactive-grid-pattern rect {
  will-change: fill, transform;
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
}

/* Focus styles for accessibility */
.interactive-grid-pattern rect:focus {
  outline: 1px solid rgba(59, 130, 246, 0.5);
  outline-offset: -1px;
}

/* Animation for grid appearance */
@keyframes gridFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.interactive-grid-pattern.fade-in {
  animation: gridFadeIn 0.5s var(--grid-transition-easing);
}

/* Utility classes for manual control */
.grid-pattern-hidden {
  opacity: 0 !important;
  pointer-events: none !important;
}

.grid-pattern-visible {
  opacity: 1 !important;
}

/* Dark theme specific adjustments (if needed) */
@media (prefers-color-scheme: dark) {
  :root {
    --grid-color: rgba(255, 255, 255, 0.02);
    --grid-hover-color: rgba(255, 255, 255, 0.05);
  }
}

/* Print styles - hide grid pattern */
@media print {
  .interactive-grid-pattern {
    display: none !important;
  }
}
